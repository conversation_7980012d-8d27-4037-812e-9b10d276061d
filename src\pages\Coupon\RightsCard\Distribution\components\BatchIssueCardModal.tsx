import { create } from '@/services/customer-membership-cards';
import { index } from '@/services/membership-card-types';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Alert, message, Progress, Typography } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

const { Text } = Typography;

interface BatchIssueCardModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  // 选中的用户ID列表
  selectedUserIds: number[];
  // 用户信息列表（用于显示）
  selectedUsers: API.Customer[];
  // 权益卡类型ID，如果是从权益卡视图发放，则传入权益卡类型ID
  cardTypeId?: number;
}

/**
 * 批量发放权益卡表单组件
 */
const BatchIssueCardModal: React.FC<BatchIssueCardModalProps> = ({
  open,
  onClose,
  onSuccess,
  selectedUserIds,
  selectedUsers,
  cardTypeId,
}) => {
  // 表单实例引用
  const formRef = useRef<ProFormInstance>();
  // 权益卡类型列表
  const [cardTypes, setCardTypes] = useState<API.MembershipCardType[]>([]);
  // 当前选中的权益卡类型
  const [selectedCardType, setSelectedCardType] =
    useState<API.MembershipCardType | null>(null);
  // 发放进度
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processResult, setProcessResult] = useState<{
    success: number;
    failed: number;
    total: number;
  } | null>(null);

  // 获取权益卡类型列表
  const fetchCardTypes = async () => {
    try {
      const response = await index({ isEnabled: true });
      if (response.errCode) {
        message.error(response.msg || '获取权益卡类型列表失败');
        return;
      }
      setCardTypes(response.data?.list || []);

      // 如果传入了cardTypeId，则设置为当前选中的权益卡类型
      if (cardTypeId) {
        const cardType = (response.data?.list || []).find(
          (item) => item.id === cardTypeId,
        );
        if (cardType) {
          setSelectedCardType(cardType);
        }
      }
    } catch (error) {
      console.error('获取权益卡类型列表失败', error);
      message.error('获取权益卡类型列表失败');
    }
  };

  // 处理权益卡类型选择变化
  const handleCardTypeChange = (value: number) => {
    const cardType = cardTypes.find((item) => item.id === value);
    setSelectedCardType(cardType || null);

    // 如果选择了有有效期的权益卡类型，自动计算到期时间
    if (cardType?.validDays) {
      const calculatedExpiryTime = moment().add(
        cardType.validDays,
        'days',
      );
      // 使用表单实例设置到期时间字段的值
      if (formRef.current) {
        formRef.current.setFieldsValue({
          expiryTime: calculatedExpiryTime,
        });
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!selectedCardType) {
      message.error('请选择权益卡类型');
      return false;
    }

    if (selectedUserIds.length === 0) {
      message.error('请选择要发放的用户');
      return false;
    }

    setIsProcessing(true);
    setProgress(0);
    setProcessResult(null);

    try {
      // 计算到期时间
      let expiryTime;
      if (selectedCardType.validDays) {
        // 如果权益卡类型有有效期，则根据权益卡类型的有效期计算
        expiryTime = moment()
          .add(selectedCardType.validDays, 'days')
          .toDate();
      } else if (values.expiryTime) {
        // 否则使用手动设置的到期时间
        expiryTime = values.expiryTime;
      }

      let successCount = 0;
      let failedCount = 0;
      const total = selectedUserIds.length;

      // 循环调用单个发放接口
      for (let i = 0; i < selectedUserIds.length; i++) {
        const customerId = selectedUserIds[i];

        try {
          // 构建请求参数
          const params: Omit<API.CustomerMembershipCard, 'id'> = {
            customerId,
            cardTypeId: values.cardTypeId,
            purchaseTime: new Date(),
            expiryTime,
            remainTimes:
              values.remainTimes !== undefined
                ? values.remainTimes
                : selectedCardType.usageLimit || -1,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // 调用API发放权益卡
          const response = await create(params);
          if (response.errCode) {
            failedCount++;
            console.error(`用户 ${customerId} 发放失败:`, response.msg);
          } else {
            successCount++;
          }
        } catch (error) {
          failedCount++;
          console.error(`用户 ${customerId} 发放失败:`, error);
        }

        // 更新进度
        setProgress(Math.round(((i + 1) / total) * 100));
      }

      setProcessResult({
        success: successCount,
        failed: failedCount,
        total,
      });

      if (failedCount === 0) {
        message.success(`批量发放成功：共发放 ${successCount} 个权益卡`);
        onSuccess();
        return true;
      } else {
        message.warning(
          `批量发放完成：成功 ${successCount} 个，失败 ${failedCount} 个`,
        );
        return false; // 不关闭弹窗，让用户查看结果
      }
    } catch (error) {
      console.error('批量发放权益卡失败', error);
      message.error('批量发放权益卡失败，请重试');
      return false;
    } finally {
      setIsProcessing(false);
    }
  };

  // 加载权益卡类型列表
  useEffect(() => {
    if (open) {
      fetchCardTypes();
      // 重置状态
      setProgress(0);
      setProcessResult(null);
      setIsProcessing(false);
    }
  }, [open]);

  return (
    <ModalForm
      title="批量发放权益卡"
      formRef={formRef}
      open={open}
      onFinish={handleSubmit}
      modalProps={{
        onCancel: onClose,
        destroyOnClose: true,
        width: 600,
        confirmLoading: isProcessing,
      }}
      initialValues={{
        cardTypeId: cardTypeId,
      }}
      submitter={{
        submitButtonProps: {
          disabled: isProcessing,
        },
      }}
    >
      {/* 选中用户信息 */}
      <div
        style={{
          marginBottom: 24,
          padding: 16,
          backgroundColor: '#f5f5f5',
          borderRadius: 6,
        }}
      >
        <Text strong>已选择 {selectedUsers.length} 个用户：</Text>
        <div style={{ marginTop: 8, maxHeight: 120, overflow: 'auto' }}>
          {selectedUsers.map((user, index) => (
            <div key={user.id} style={{ fontSize: 12, color: '#666' }}>
              {index + 1}. {user.nickname || '未设置昵称'} ({user.phone})
            </div>
          ))}
        </div>
      </div>

      {/* 权益卡类型选择 */}
      <ProFormSelect
        name="cardTypeId"
        label="权益卡类型"
        rules={[{ required: true, message: '请选择权益卡类型' }]}
        options={cardTypes.map((item) => ({
          label: `${item.name}（价值${item.price}元）`,
          value: item.id,
        }))}
        fieldProps={{
          onChange: handleCardTypeChange,
        }}
        disabled={!!cardTypeId || isProcessing}
      />

      {/* 显示权益卡类型的详细信息 */}
      {selectedCardType && (
        <div
          style={{
            marginBottom: 24,
            padding: 16,
            backgroundColor: '#f5f5f5',
            borderRadius: 6,
          }}
        >
          <p>
            <strong>卡片名称:</strong> {selectedCardType.name}
          </p>
          <p>
            <strong>价值:</strong> ¥{selectedCardType.price}
          </p>
          <p>
            <strong>有效期:</strong>{' '}
            {selectedCardType.validDays || '无限制'}天
          </p>
          <p>
            <strong>可用次数:</strong> {selectedCardType.usageLimit || '不限'}次
          </p>
          {selectedCardType.description && (
            <p>
              <strong>权益描述:</strong> {selectedCardType.description}
            </p>
          )}
        </div>
      )}

      {/* 到期时间设置 */}
      <ProFormDatePicker
        name="expiryTime"
        label="到期时间"
        placeholder={
          selectedCardType?.validDays
            ? '已根据权益卡有效期自动计算'
            : '请选择到期时间'
        }
        tooltip={
          selectedCardType?.validDays
            ? '权益卡有有效期，到期时间已自动计算'
            : '请选择到期时间，不设置则表示无期限'
        }
        disabled={!!selectedCardType?.validDays || isProcessing}
      />

      {/* 可用次数设置 */}
      <ProFormDigit
        name="remainTimes"
        label="可用次数"
        placeholder={
          selectedCardType?.usageLimit
            ? `默认${selectedCardType.usageLimit}次`
            : '不限次数'
        }
        tooltip="不填写则使用权益卡默认次数，填写-1表示不限次数"
        min={-1}
        fieldProps={{ precision: 0 }}
        disabled={isProcessing}
      />

      {/* 发放进度 */}
      {isProcessing && (
        <div style={{ marginBottom: 16 }}>
          <Text>正在发放权益卡...</Text>
          <Progress percent={progress} status="active" />
        </div>
      )}

      {/* 发放结果 */}
      {processResult && (
        <Alert
          message={`发放完成：成功 ${processResult.success} 个，失败 ${processResult.failed} 个`}
          type={processResult.failed === 0 ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
    </ModalForm>
  );
};

export default BatchIssueCardModal;
