import { index } from '@/services/coupons';
import { getUsersByCoupon } from '@/services/customer-coupons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Space, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import UserList from './UserList';
import BatchIssueCouponModal from '../components/BatchIssueCouponModal';

/**
 * 代金券视角组件
 */
const CardView: React.FC = () => {
  // 表格操作引用
  const actionRef = useRef<ActionType>();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentCouponId, setCurrentCouponId] = useState<number>(0);
  const [currentCouponName, setCurrentCouponName] = useState<string>('');

  // 批量发放模态框状态
  const [batchIssueModalVisible, setBatchIssueModalVisible] = useState<boolean>(false);

  // 处理查看用户列表
  const handleViewUsers = (record: API.Coupon) => {
    setCurrentCouponId(record.id);
    setCurrentCouponName(`${record.amount}元代金券`);
    setDrawerVisible(true);
  };

  // 获取代金券发放数量
  const [couponCounts, setCouponCounts] = useState<Record<number, number>>({});

  // 加载代金券发放数量
  const loadCouponCounts = async (coupons: API.Coupon[]) => {
    const counts: Record<number, number> = {};

    // 并行获取每个代金券的发放数量
    await Promise.all(
      coupons.map(async (coupon) => {
        try {
          const response = await getUsersByCoupon(coupon.id, {
            current: 1,
            pageSize: 1,
          });
          if (!response.errCode) {
            counts[coupon.id] = response.data?.total || 0;
          }
        } catch (error) {
          console.error(`获取代金券 ${coupon.id} 的发放数量失败`, error);
        }
      }),
    );

    setCouponCounts(counts);
  };

  // 表格列定义
  const columns: ProColumns<API.Coupon>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '面值',
      dataIndex: 'amount',
      width: 100,
      valueType: 'money',
      search: false,
    },
    {
      title: '使用门槛',
      dataIndex: 'threshold',
      width: 120,
      valueType: 'money',
      search: false,
    },
    {
      title: '有效期(天)',
      dataIndex: 'validDays',
      width: 100,
      search: false,
    },
    {
      title: '发放数量',
      dataIndex: 'id',
      hideInSearch: true,
      width: 100,
      render: (_, record) => {
        const count = couponCounts[record.id] || 0;
        return <Tag color="blue">{count}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewUsers(record)}>
            管理
          </Button>
          <Button
            type="link"
            onClick={() => {
              setCurrentCouponId(record.id);
              setBatchIssueModalVisible(true);
            }}
          >
            批量发放
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Coupon>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const response = await index({
            ...params,
            isEnabled: true,
          });

          if (response.errCode) {
            message.error(response.msg || '获取代金券列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          // 获取代金券列表后，加载每个代金券的发放数量
          const coupons = response.data?.list || [];
          if (coupons.length > 0) {
            loadCouponCounts(coupons);
          }

          return {
            data: coupons,
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 用户列表抽屉 */}
      <Drawer
        title={`${currentCouponName} - 用户管理`}
        width={1200}
        placement="right"
        onClose={() => {
          setCurrentCouponId(0);
          setCurrentCouponName('');
          setDrawerVisible(false);
        }}
        open={drawerVisible}
        destroyOnClose={true}
      >
        <UserList couponId={currentCouponId} />
      </Drawer>

      {/* 批量发放代金券模态框 */}
      <BatchIssueCouponModal
        open={batchIssueModalVisible}
        onClose={() => setBatchIssueModalVisible(false)}
        onSuccess={() => {
          setBatchIssueModalVisible(false);
          actionRef.current?.reload();
        }}
        couponId={currentCouponId}
      />
    </>
  );
};

export default CardView;
