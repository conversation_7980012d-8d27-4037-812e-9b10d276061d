import { getCustomers } from '@/services/customers';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Space } from 'antd';
import React, { useRef, useState } from 'react';
import CardList from './CardList';
import BatchIssueCouponModal from '../components/BatchIssueCouponModal';

/**
 * 用户视角组件
 */
const UserView: React.FC = () => {
  // 表格操作引用
  const actionRef = useRef<ActionType>();

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentUserId, setCurrentUserId] = useState<number>(0);
  const [currentUserName, setCurrentUserName] = useState<string>('');

  // 批量发放状态
  const [batchIssueModalVisible, setBatchIssueModalVisible] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<API.Customer[]>([]);

  // 处理查看代金券列表
  const handleViewCoupons = (record: API.Customer) => {
    setCurrentUserId(record.id);
    setCurrentUserName(record.nickname || `用户${record.id}`);
    setDrawerVisible(true);
  };

  // 处理行选择变化
  const handleRowSelectionChange = (selectedRowKeys: React.Key[], selectedRows: API.Customer[]) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedUsers(selectedRows);
  };

  // 处理批量发放
  const handleBatchIssue = () => {
    if (selectedUsers.length === 0) {
      message.warning('请先选择要发放代金券的用户');
      return;
    }
    setBatchIssueModalVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<API.Customer>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      width: 150,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      width: 150,
      render: (text) => text || '-',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: 80,
      search: false,
      valueEnum: {
        0: { text: '女' },
        1: { text: '男' },
        2: { text: '保密' },
      },
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      width: 100,
      valueEnum: {
        0: { text: '非会员', status: 'Default' },
        1: { text: '会员', status: 'Success' },
      },
    },
    {
      title: '积分',
      dataIndex: 'points',
      width: 100,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        0: { text: '禁用', status: 'Error' },
        1: { text: '启用', status: 'Success' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button type="link" onClick={() => handleViewCoupons(record)}>
            管理代金券
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Customer>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        rowSelection={{
          selectedRowKeys,
          onChange: handleRowSelectionChange,
        }}
        tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => (
          <span>
            已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a> 个用户
            <a style={{ marginLeft: 24 }} onClick={onCleanSelected}>
              取消选择
            </a>
          </span>
        )}
        tableAlertOptionRender={() => {
          return (
            <Space size={16}>
              <Button type="primary" onClick={handleBatchIssue}>
                批量发放代金券
              </Button>
            </Space>
          );
        }}
        request={async (params) => {
          const response = await getCustomers({
            ...params,
            status: 1, // 只查询启用状态的用户
          });

          if (response.errCode) {
            message.error(response.msg || '获取用户列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />

      {/* 代金券列表抽屉 */}
      <Drawer
        title={`${currentUserName} - 代金券列表`}
        width={1200}
        placement="right"
        onClose={() => {
          setCurrentUserId(0);
          setCurrentUserName('');
          setDrawerVisible(false);
        }}
        open={drawerVisible}
        destroyOnClose={true}
      >
        <CardList userId={currentUserId} />
      </Drawer>

      {/* 批量发放代金券模态框 */}
      <BatchIssueCouponModal
        open={batchIssueModalVisible}
        onClose={() => setBatchIssueModalVisible(false)}
        onSuccess={() => {
          setBatchIssueModalVisible(false);
          setSelectedRowKeys([]);
          setSelectedUsers([]);
          actionRef.current?.reload();
        }}
        selectedUserIds={selectedUsers.map(user => user.id)}
        selectedUsers={selectedUsers}
      />
    </>
  );
};

export default UserView;
