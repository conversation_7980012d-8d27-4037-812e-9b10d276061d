import { ApplicableScope } from '@/constant';
import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/coupons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space, Switch, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const TypeComponent: React.FC<{
  dictionarie: DictionarieState;
}> = ({}) => {
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.Coupon>();
  const actionRef = useRef<ActionType>();

  const handleSave = async (values: API.Coupon) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg || '操作失败');
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Coupon) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg || '删除失败');
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  // 处理状态切换
  const handleStatusChange = async (checked: boolean, record: API.Coupon) => {
    const response = await update(record.id, { isEnabled: checked });
    if (response.errCode) {
      message.error(response.msg || '状态更新失败');
    } else {
      message.success('状态更新成功');
      actionRef.current?.reload();
    }
  };

  const columns: ProColumns<API.Coupon>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '面值',
      dataIndex: 'amount',
      width: 100,
      valueType: 'money',
      search: false,
    },
    {
      title: '使用门槛',
      dataIndex: 'threshold',
      width: 120,
      valueType: 'money',
      search: false,
    },
    {
      title: '可用次数',
      dataIndex: 'usageLimit',
      hideInSearch: true,
      width: 100,
      render: (_, record) =>
        record.usageLimit ? `${record.usageLimit}次` : '不限',
    },
    {
      title: '售价(元)',
      dataIndex: 'price',
      width: 100,
      valueType: 'money',
      search: false,
    },
    {
      title: '有效期(天)',
      dataIndex: 'validDays',
      width: 100,
      search: false,
    },
    {
      title: '适用范围',
      dataIndex: 'applicableScope',
      width: 150,
      search: false,
      render: (_scope, record) => {
        // 根据适用范围类型显示不同的标签
        switch (record.applicableScope) {
          case ApplicableScope.不限:
            return <Tag color="green">全部商品和服务</Tag>;
          case ApplicableScope.所有商品:
            return <Tag color="blue">所有商品</Tag>;
          case ApplicableScope.所有服务:
            return <Tag color="blue">所有服务</Tag>;
          case ApplicableScope.指定服务类别:
            return <Tag color="orange">指定服务类别</Tag>;
          case ApplicableScope.指定服务品牌:
            return <Tag color="orange">指定服务品牌</Tag>;
          case ApplicableScope.指定服务:
            return <Tag color="purple">指定服务</Tag>;
          case ApplicableScope.指定商品类别: {
            return <Tag color="orange">指定商品类别</Tag>;
          }
          case 'product':
            return <Tag color="blue">指定商品</Tag>;
          default:
            return <Tag color="purple">未指定</Tag>;
        }
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      search: false,
    },
    {
      title: '使用说明',
      dataIndex: 'description',
      hideInSearch: true,
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'isEnabled',
      width: 100,
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Error' },
      },
      render: (_, record) => (
        <Switch
          checked={record.isEnabled}
          onChange={(checked) => handleStatusChange(checked, record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Coupon>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const response = await index(params);

          if (response.errCode) {
            message.error(response.msg || '获取代金券列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增代金券
          </Button>,
        ]}
      />
      {/*
        使用key属性强制React在current变化时重新创建组件
        这样可以确保组件内部状态被完全重置
      */}
      <EditModal
        key={current ? `edit-${current.id}` : 'create-new'}
        open={modalVisible}
        info={current}
        onClose={() => {
          setCurrent(undefined);
          setModalVisible(false);
        }}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(TypeComponent);
