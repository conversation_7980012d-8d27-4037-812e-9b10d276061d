import { index } from '@/services/coupons';
import { batchCreate } from '@/services/customer-coupons';
import { getCustomers } from '@/services/customers';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Alert, Button, message, Space, Typography, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

const { Text } = Typography;

interface BatchIssueCouponModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  // 代金券ID，如果是从代金券视图发放，则传入代金券ID
  couponId?: number;
}

/**
 * 批量发放代金券表单组件
 */
const BatchIssueCouponModal: React.FC<BatchIssueCouponModalProps> = ({
  open,
  onClose,
  onSuccess,
  couponId,
}) => {
  // 表单实例引用
  const formRef = useRef<ProFormInstance>();
  // 代金券列表
  const [coupons, setCoupons] = useState<API.Coupon[]>([]);
  // 当前选中的代金券
  const [selectedCoupon, setSelectedCoupon] = useState<API.Coupon | null>(null);
  // 用户输入方式：manual-手动输入，excel-Excel上传
  const [inputMethod, setInputMethod] = useState<'manual' | 'excel'>('manual');
  // 手机号列表
  const [phoneList, setPhoneList] = useState<string[]>([]);
  // 用户验证结果
  const [userValidation, setUserValidation] = useState<{
    validUsers: API.Customer[];
    invalidPhones: string[];
  }>({ validUsers: [], invalidPhones: [] });
  // 是否正在验证用户
  const [validatingUsers, setValidatingUsers] = useState(false);

  // 获取代金券列表
  const fetchCoupons = async () => {
    try {
      const response = await index({ isEnabled: true });
      if (response.errCode) {
        message.error(response.msg || '获取代金券列表失败');
        return;
      }
      setCoupons(response.data?.list || []);

      // 如果传入了couponId，则设置为当前选中的代金券
      if (couponId) {
        const coupon = (response.data?.list || []).find(
          (item) => item.id === couponId,
        );
        if (coupon) {
          setSelectedCoupon(coupon);
        }
      }
    } catch (error) {
      console.error('获取代金券列表失败', error);
      message.error('获取代金券列表失败');
    }
  };

  // 处理Excel文件上传
  const handleExcelUpload = (file: File) => {
    // 暂时提示用户Excel功能正在开发中
    message.info('Excel上传功能正在开发中，请使用手动输入方式');
    return false; // 阻止自动上传
  };

  // 处理手动输入的手机号
  const handleManualInput = (value: string) => {
    const phones = value
      .split('\n')
      .map(phone => phone.trim())
      .filter(phone => phone && /^1[3-9]\d{9}$/.test(phone));
    setPhoneList(phones);
  };

  // 验证用户手机号
  const validateUsers = async () => {
    if (phoneList.length === 0) {
      message.warning('请先输入或上传手机号');
      return;
    }

    setValidatingUsers(true);
    try {
      const validUsers: API.Customer[] = [];
      const invalidPhones: string[] = [];

      // 批量查询用户
      for (const phone of phoneList) {
        try {
          const response = await getCustomers({ phone });
          if (!response.errCode && response.data?.list?.length > 0) {
            validUsers.push(response.data.list[0]);
          } else {
            invalidPhones.push(phone);
          }
        } catch (error) {
          invalidPhones.push(phone);
        }
      }

      setUserValidation({ validUsers, invalidPhones });
      message.success(`验证完成：${validUsers.length} 个有效用户，${invalidPhones.length} 个无效手机号`);
    } catch (error) {
      console.error('验证用户失败', error);
      message.error('验证用户失败，请重试');
    } finally {
      setValidatingUsers(false);
    }
  };

  // 处理代金券选择变化
  const handleCouponChange = (value: number) => {
    const coupon = coupons.find((item) => item.id === value);
    setSelectedCoupon(coupon || null);

    // 如果选择了有有效期的代金券，自动计算到期时间
    if (coupon?.validDays) {
      const calculatedExpiryTime = moment().add(coupon.validDays, 'days');
      // 使用表单实例设置到期时间字段的值
      if (formRef.current) {
        formRef.current.setFieldsValue({
          expiryTime: calculatedExpiryTime,
        });
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!selectedCoupon) {
      message.error('请选择代金券');
      return false;
    }

    if (userValidation.validUsers.length === 0) {
      message.error('请先验证用户手机号');
      return false;
    }

    try {
      // 计算到期时间
      let expiryTime;
      if (selectedCoupon.validDays) {
        // 如果代金券有有效期，则根据代金券的有效期计算
        expiryTime = moment().add(selectedCoupon.validDays, 'days').toDate();
      } else if (values.expiryTime) {
        // 否则使用手动设置的到期时间
        expiryTime = values.expiryTime;
      }

      // 构建请求参数
      const params = {
        couponId: values.couponId,
        customerIds: userValidation.validUsers.map(user => user.id),
        expiryTime,
        remainTimes: values.remainTimes !== undefined 
          ? values.remainTimes 
          : selectedCoupon.usageLimit || -1,
      };

      // 调用批量发放API
      const response = await batchCreate(params);
      if (response.errCode) {
        message.error(response.msg || '批量发放代金券失败');
        return false;
      }

      const result = response.data;
      if (result?.failedCount > 0) {
        message.warning(`批量发放完成：成功 ${result.successCount} 个，失败 ${result.failedCount} 个`);
      } else {
        message.success(`批量发放成功：共发放 ${result?.successCount} 个代金券`);
      }
      
      onSuccess();
      return true;
    } catch (error) {
      console.error('批量发放代金券失败', error);
      message.error('批量发放代金券失败，请重试');
      return false;
    }
  };

  // 加载代金券列表
  useEffect(() => {
    if (open) {
      fetchCoupons();
      // 重置状态
      setPhoneList([]);
      setUserValidation({ validUsers: [], invalidPhones: [] });
      setInputMethod('manual');
    }
  }, [open]);

  return (
    <ModalForm
      title="批量发放代金券"
      formRef={formRef}
      open={open}
      onFinish={handleSubmit}
      modalProps={{
        onCancel: onClose,
        destroyOnClose: true,
        width: 800,
      }}
      initialValues={{
        couponId: couponId,
        inputMethod: 'manual',
      }}
    >
      {/* 代金券选择 */}
      <ProFormSelect
        name="couponId"
        label="代金券"
        rules={[{ required: true, message: '请选择代金券' }]}
        options={coupons.map((item) => ({
          label: `${item.amount}元代金券（满${item.threshold}元可用）`,
          value: item.id,
        }))}
        fieldProps={{
          onChange: handleCouponChange,
        }}
        disabled={!!couponId}
      />

      {/* 显示代金券的详细信息 */}
      {selectedCoupon && (
        <div style={{ marginBottom: 24, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
          <p><strong>面值:</strong> ¥{selectedCoupon.amount}</p>
          <p><strong>使用门槛:</strong> ¥{selectedCoupon.threshold}</p>
          <p><strong>有效期:</strong> {selectedCoupon.validDays || '无限制'}天</p>
          <p><strong>可用次数:</strong> {selectedCoupon.usageLimit || '不限'}次</p>
          {selectedCoupon.description && (
            <p><strong>使用说明:</strong> {selectedCoupon.description}</p>
          )}
        </div>
      )}

      {/* 用户输入方式选择 */}
      <ProFormRadio.Group
        name="inputMethod"
        label="用户输入方式"
        options={[
          { label: '手动输入', value: 'manual' },
          { label: 'Excel上传', value: 'excel' },
        ]}
        fieldProps={{
          onChange: (e) => setInputMethod(e.target.value),
        }}
      />

      {/* 手动输入手机号 */}
      {inputMethod === 'manual' && (
        <ProFormTextArea
          name="phoneNumbers"
          label="用户手机号"
          placeholder="请输入用户手机号，每行一个"
          rules={[{ required: true, message: '请输入用户手机号' }]}
          fieldProps={{
            rows: 6,
            onChange: (e) => handleManualInput(e.target.value),
          }}
        />
      )}

      {/* Excel文件上传 */}
      {inputMethod === 'excel' && (
        <div style={{ marginBottom: 24 }}>
          <label style={{ display: 'block', marginBottom: 8 }}>Excel文件上传</label>
          <Upload
            accept=".xlsx,.xls"
            beforeUpload={handleExcelUpload}
            showUploadList={false}
          >
            <Button icon={<UploadOutlined />}>选择Excel文件</Button>
          </Upload>
          <div style={{ marginTop: 8, color: '#666', fontSize: 12 }}>
            请确保Excel文件第一列为用户手机号
          </div>
        </div>
      )}

      {/* 手机号列表预览和验证 */}
      {phoneList.length > 0 && (
        <div style={{ marginBottom: 24 }}>
          <Space style={{ marginBottom: 8 }}>
            <Text strong>手机号列表 ({phoneList.length}个)</Text>
            <Button 
              type="primary" 
              size="small" 
              onClick={validateUsers}
              loading={validatingUsers}
            >
              验证用户
            </Button>
          </Space>
          <div style={{ maxHeight: 120, overflow: 'auto', border: '1px solid #d9d9d9', padding: 8, borderRadius: 4 }}>
            {phoneList.map((phone, index) => (
              <div key={index} style={{ fontSize: 12, color: '#666' }}>
                {phone}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 用户验证结果 */}
      {(userValidation.validUsers.length > 0 || userValidation.invalidPhones.length > 0) && (
        <div style={{ marginBottom: 24 }}>
          {userValidation.validUsers.length > 0 && (
            <Alert
              message={`找到 ${userValidation.validUsers.length} 个有效用户`}
              type="success"
              showIcon
              style={{ marginBottom: 8 }}
            />
          )}
          {userValidation.invalidPhones.length > 0 && (
            <Alert
              message={`${userValidation.invalidPhones.length} 个无效手机号：${userValidation.invalidPhones.join(', ')}`}
              type="warning"
              showIcon
            />
          )}
        </div>
      )}

      {/* 到期时间设置 */}
      <ProFormDatePicker
        name="expiryTime"
        label="到期时间"
        placeholder={
          selectedCoupon?.validDays
            ? '已根据代金券有效期自动计算'
            : '请选择到期时间'
        }
        tooltip={
          selectedCoupon?.validDays
            ? '代金券有有效期，到期时间已自动计算'
            : '请选择到期时间，不设置则表示无期限'
        }
        disabled={!!selectedCoupon?.validDays}
      />

      {/* 可用次数设置 */}
      <ProFormDigit
        name="remainTimes"
        label="可用次数"
        placeholder={
          selectedCoupon?.usageLimit
            ? `默认${selectedCoupon.usageLimit}次`
            : '不限次数'
        }
        tooltip="不填写则使用代金券默认次数，填写-1表示不限次数"
        min={-1}
        fieldProps={{ precision: 0 }}
      />
    </ModalForm>
  );
};

export default BatchIssueCouponModal;
