import { index } from '@/services/coupons';
import { create } from '@/services/customer-coupons';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Alert, message, Progress, Typography } from 'antd';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';

const { Text } = Typography;

interface BatchIssueCouponModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  // 选中的用户ID列表
  selectedUserIds: number[];
  // 用户信息列表（用于显示）
  selectedUsers: API.Customer[];
  // 代金券ID，如果是从代金券视图发放，则传入代金券ID
  couponId?: number;
}

/**
 * 批量发放代金券表单组件
 */
const BatchIssueCouponModal: React.FC<BatchIssueCouponModalProps> = ({
  open,
  onClose,
  onSuccess,
  selectedUserIds,
  selectedUsers,
  couponId,
}) => {
  // 表单实例引用
  const formRef = useRef<ProFormInstance>();
  // 代金券列表
  const [coupons, setCoupons] = useState<API.Coupon[]>([]);
  // 当前选中的代金券
  const [selectedCoupon, setSelectedCoupon] = useState<API.Coupon | null>(null);
  // 发放进度
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processResult, setProcessResult] = useState<{
    success: number;
    failed: number;
    total: number;
  } | null>(null);

  // 获取代金券列表
  const fetchCoupons = async () => {
    try {
      const response = await index({ isEnabled: true });
      if (response.errCode) {
        message.error(response.msg || '获取代金券列表失败');
        return;
      }
      setCoupons(response.data?.list || []);

      // 如果传入了couponId，则设置为当前选中的代金券
      if (couponId) {
        const coupon = (response.data?.list || []).find(
          (item) => item.id === couponId,
        );
        if (coupon) {
          setSelectedCoupon(coupon);
        }
      }
    } catch (error) {
      console.error('获取代金券列表失败', error);
      message.error('获取代金券列表失败');
    }
  };

  // 处理代金券选择变化
  const handleCouponChange = (value: number) => {
    const coupon = coupons.find((item) => item.id === value);
    setSelectedCoupon(coupon || null);

    // 如果选择了有有效期的代金券，自动计算到期时间
    if (coupon?.validDays) {
      const calculatedExpiryTime = moment().add(coupon.validDays, 'days');
      // 使用表单实例设置到期时间字段的值
      if (formRef.current) {
        formRef.current.setFieldsValue({
          expiryTime: calculatedExpiryTime,
        });
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!selectedCoupon) {
      message.error('请选择代金券');
      return false;
    }

    if (selectedUserIds.length === 0) {
      message.error('请选择要发放的用户');
      return false;
    }

    setIsProcessing(true);
    setProgress(0);
    setProcessResult(null);

    try {
      // 计算到期时间
      let expiryTime;
      if (selectedCoupon.validDays) {
        // 如果代金券有有效期，则根据代金券的有效期计算
        expiryTime = moment().add(selectedCoupon.validDays, 'days').toDate();
      } else if (values.expiryTime) {
        // 否则使用手动设置的到期时间
        expiryTime = values.expiryTime;
      }

      let successCount = 0;
      let failedCount = 0;
      const total = selectedUserIds.length;

      // 循环调用单个发放接口
      for (let i = 0; i < selectedUserIds.length; i++) {
        const customerId = selectedUserIds[i];
        
        try {
          // 构建请求参数
          const params: Omit<API.CustomerCoupon, 'id'> = {
            customerId,
            couponId: values.couponId,
            receiveTime: new Date(),
            expiryTime,
            remainTimes:
              values.remainTimes !== undefined
                ? values.remainTimes
                : selectedCoupon.usageLimit || -1,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          // 调用API发放代金券
          const response = await create(params);
          if (response.errCode) {
            failedCount++;
            console.error(`用户 ${customerId} 发放失败:`, response.msg);
          } else {
            successCount++;
          }
        } catch (error) {
          failedCount++;
          console.error(`用户 ${customerId} 发放失败:`, error);
        }

        // 更新进度
        setProgress(Math.round(((i + 1) / total) * 100));
      }

      setProcessResult({
        success: successCount,
        failed: failedCount,
        total,
      });

      if (failedCount === 0) {
        message.success(`批量发放成功：共发放 ${successCount} 个代金券`);
        onSuccess();
        return true;
      } else {
        message.warning(`批量发放完成：成功 ${successCount} 个，失败 ${failedCount} 个`);
        return false; // 不关闭弹窗，让用户查看结果
      }
    } catch (error) {
      console.error('批量发放代金券失败', error);
      message.error('批量发放代金券失败，请重试');
      return false;
    } finally {
      setIsProcessing(false);
    }
  };

  // 加载代金券列表
  useEffect(() => {
    if (open) {
      fetchCoupons();
      // 重置状态
      setProgress(0);
      setProcessResult(null);
      setIsProcessing(false);
    }
  }, [open]);

  return (
    <ModalForm
      title="批量发放代金券"
      formRef={formRef}
      open={open}
      onFinish={handleSubmit}
      modalProps={{
        onCancel: onClose,
        destroyOnClose: true,
        width: 600,
        confirmLoading: isProcessing,
      }}
      initialValues={{
        couponId: couponId,
      }}
      submitter={{
        submitButtonProps: {
          disabled: isProcessing,
        },
      }}
    >
      {/* 选中用户信息 */}
      <div style={{ marginBottom: 24, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
        <Text strong>已选择 {selectedUsers.length} 个用户：</Text>
        <div style={{ marginTop: 8, maxHeight: 120, overflow: 'auto' }}>
          {selectedUsers.map((user, index) => (
            <div key={user.id} style={{ fontSize: 12, color: '#666' }}>
              {index + 1}. {user.nickname || '未设置昵称'} ({user.phone})
            </div>
          ))}
        </div>
      </div>

      {/* 代金券选择 */}
      <ProFormSelect
        name="couponId"
        label="代金券"
        rules={[{ required: true, message: '请选择代金券' }]}
        options={coupons.map((item) => ({
          label: `${item.amount}元代金券（满${item.threshold}元可用）`,
          value: item.id,
        }))}
        fieldProps={{
          onChange: handleCouponChange,
        }}
        disabled={!!couponId || isProcessing}
      />

      {/* 显示代金券的详细信息 */}
      {selectedCoupon && (
        <div style={{ marginBottom: 24, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
          <p><strong>面值:</strong> ¥{selectedCoupon.amount}</p>
          <p><strong>使用门槛:</strong> ¥{selectedCoupon.threshold}</p>
          <p><strong>有效期:</strong> {selectedCoupon.validDays || '无限制'}天</p>
          <p><strong>可用次数:</strong> {selectedCoupon.usageLimit || '不限'}次</p>
          {selectedCoupon.description && (
            <p><strong>使用说明:</strong> {selectedCoupon.description}</p>
          )}
        </div>
      )}

      {/* 到期时间设置 */}
      <ProFormDatePicker
        name="expiryTime"
        label="到期时间"
        placeholder={
          selectedCoupon?.validDays
            ? '已根据代金券有效期自动计算'
            : '请选择到期时间'
        }
        tooltip={
          selectedCoupon?.validDays
            ? '代金券有有效期，到期时间已自动计算'
            : '请选择到期时间，不设置则表示无期限'
        }
        disabled={!!selectedCoupon?.validDays || isProcessing}
      />

      {/* 可用次数设置 */}
      <ProFormDigit
        name="remainTimes"
        label="可用次数"
        placeholder={
          selectedCoupon?.usageLimit
            ? `默认${selectedCoupon.usageLimit}次`
            : '不限次数'
        }
        tooltip="不填写则使用代金券默认次数，填写-1表示不限次数"
        min={-1}
        fieldProps={{ precision: 0 }}
        disabled={isProcessing}
      />

      {/* 发放进度 */}
      {isProcessing && (
        <div style={{ marginBottom: 16 }}>
          <Text>正在发放代金券...</Text>
          <Progress percent={progress} status="active" />
        </div>
      )}

      {/* 发放结果 */}
      {processResult && (
        <Alert
          message={`发放完成：成功 ${processResult.success} 个，失败 ${processResult.failed} 个`}
          type={processResult.failed === 0 ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}
    </ModalForm>
  );
};

export default BatchIssueCouponModal;
